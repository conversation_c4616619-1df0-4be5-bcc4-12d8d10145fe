# Bharatvan CMS User Guide

## 🌱 Welcome to Bharatvan Content Management System

This guide will help you effectively manage all content on the Bharatvan farmland website using our user-friendly CMS interface.

## 📋 Table of Contents

1. [Getting Started](#getting-started)
2. [Dashboard Overview](#dashboard-overview)
3. [Content Management](#content-management)
4. [Publishing Workflow](#publishing-workflow)
5. [Best Practices](#best-practices)
6. [Troubleshooting](#troubleshooting)

## 🚀 Getting Started

### Accessing the CMS

1. **Local Development**: Navigate to `http://localhost:5174/admin/`
2. **Production**: Navigate to `https://your-domain.com/admin/`
3. **Login**: Use your authorized credentials to access the CMS

### First Time Setup

1. Ensure you have proper permissions to edit content
2. Familiarize yourself with the dashboard layout
3. Review the content structure and organization

## 📊 Dashboard Overview

### Main Navigation

The CMS is organized into logical sections:

- **📄 Pages**: Static page content (Home, About, Services, etc.)
- **🚜 Farms**: Farm listings and investment opportunities
- **📝 Blog Posts**: Articles and news content
- **💬 Testimonials**: Customer reviews and success stories
- **📰 Media Logos**: Press coverage and media mentions
- **⚙️ Site Settings**: Global configuration and contact information

### Content Status

- **Draft**: Content being worked on (not visible on website)
- **In Review**: Content ready for approval
- **Published**: Live content visible on website

## 📝 Content Management

### 1. Homepage Management

**Location**: Pages → Home Page

**Key Sections**:
- **Hero Section**: Main banner with title, subtitle, and call-to-action buttons
- **Introduction**: Features and benefits overview
- **Featured Farms**: Highlighted investment opportunities
- **Why Choose Us**: Statistics and value propositions
- **Media Logos**: Press coverage display
- **Blog Section**: Latest articles preview

**Tips**:
- Keep hero titles under 60 characters for better display
- Use high-quality images (minimum 1920x1080 for hero backgrounds)
- Update statistics regularly to maintain credibility

### 2. Farm Listings Management

**Location**: Farms → Farm Listings

**Required Fields**:
- **Farm Name**: Clear, descriptive name
- **Location**: State, Country format
- **Starting Price**: Minimum investment in Indian Rupees
- **Description**: 100-1000 characters describing the opportunity
- **Images**: High-quality photos (minimum 3, maximum 10)
- **Status**: ongoing/upcoming/sold-out

**Best Practices**:
- Use professional photography for farm images
- Include diverse shots: aerial views, crops, facilities
- Keep descriptions factual and compelling
- Update availability status regularly

### 3. Blog Post Creation

**Location**: Blog Posts → Blog Posts

**Content Structure**:
- **Title**: SEO-friendly, 10-150 characters
- **Excerpt**: Compelling summary, 50-500 characters
- **Author Information**: Name, bio, and photo
- **Category**: Choose from predefined options
- **Tags**: Relevant keywords for searchability
- **Content**: Full article in Markdown format

**Writing Guidelines**:
- Use clear, engaging headlines
- Include relevant images throughout the content
- Optimize for SEO with natural keyword usage
- Maintain consistent tone and style

### 4. Testimonials Management

**Location**: Testimonials → Customer Testimonials

**Required Information**:
- **Customer Name**: Full name
- **Location**: City, State format
- **Testimonial Text**: 50-1000 characters
- **Rating**: 1-5 stars
- **Customer Photo**: Professional headshot
- **Investment Details**: Farm and amount invested

**Quality Standards**:
- Verify all testimonials are authentic
- Use high-quality, professional photos
- Include specific details about the investment
- Mark verified testimonials appropriately

### 5. Contact Information Updates

**Location**: Site Settings → Site Settings

**Key Areas**:
- **Company Details**: Name, address, phone, email
- **Social Media**: Platform URLs (leave blank if not available)
- **Office Locations**: Multiple office addresses
- **Business Information**: Registration details

**Important Notes**:
- Ensure all contact information is current
- Validate phone numbers and email addresses
- Update social media links when accounts change
- Maintain consistency across all platforms

## 🔄 Publishing Workflow

### Editorial Workflow

1. **Create Draft**: Start with draft status for new content
2. **Review Process**: Submit for review when ready
3. **Approval**: Authorized users approve content
4. **Publish**: Content goes live on the website

### Content Review Checklist

- [ ] Spelling and grammar check
- [ ] Image quality and optimization
- [ ] SEO optimization (titles, descriptions, keywords)
- [ ] Mobile responsiveness preview
- [ ] Link functionality verification
- [ ] Brand consistency check

## ✅ Best Practices

### Content Quality

1. **Consistency**: Maintain consistent tone, style, and formatting
2. **Accuracy**: Verify all facts, figures, and contact information
3. **Relevance**: Ensure content serves the target audience
4. **Freshness**: Regular updates keep content current and engaging

### SEO Optimization

1. **Keywords**: Use relevant keywords naturally in content
2. **Meta Descriptions**: Write compelling excerpts for search results
3. **Image Alt Text**: Provide descriptive alt text for all images
4. **URL Structure**: Use SEO-friendly slugs for blog posts

### Image Guidelines

1. **Quality**: Use high-resolution, professional images
2. **Format**: Prefer JPEG for photos, PNG for graphics with transparency
3. **Size**: Optimize file sizes for web performance
4. **Relevance**: Ensure images support and enhance the content

### Mobile Optimization

1. **Preview**: Always preview content on mobile devices
2. **Text Size**: Ensure readability on small screens
3. **Image Scaling**: Verify images display properly on mobile
4. **Navigation**: Test user experience on touch devices

## 🔧 Troubleshooting

### Common Issues

**Problem**: CMS won't load
**Solution**: 
- Check internet connection
- Clear browser cache
- Try a different browser
- Contact technical support

**Problem**: Images won't upload
**Solution**:
- Check file size (max 10MB)
- Verify file format (JPG, PNG, GIF, WebP)
- Ensure stable internet connection
- Try refreshing the page

**Problem**: Content not saving
**Solution**:
- Check for required fields
- Verify character limits
- Ensure proper formatting
- Try saving again after a few minutes

**Problem**: Preview not working
**Solution**:
- Refresh the preview window
- Check for JavaScript errors in browser console
- Verify content formatting
- Contact technical support if issue persists

### Getting Help

1. **Documentation**: Refer to this guide and technical documentation
2. **Support Team**: Contact the technical team for assistance
3. **Training**: Request additional training if needed
4. **Feedback**: Provide feedback for CMS improvements

## 📞 Support Contact

For technical support or questions about the CMS:

- **Email**: <EMAIL>
- **Phone**: 8819855558
- **Documentation**: Check the `/docs` folder for technical guides

## 🎯 Quick Start Checklist

### For New Content Editors

- [ ] Access CMS at `/admin/` URL
- [ ] Familiarize yourself with the dashboard layout
- [ ] Review existing content structure
- [ ] Practice creating a draft blog post
- [ ] Test image upload functionality
- [ ] Understand the editorial workflow

### For Administrators

- [ ] Verify all content collections are properly configured
- [ ] Test user permissions and access controls
- [ ] Ensure backup and version control systems are working
- [ ] Train content editors on CMS usage
- [ ] Set up monitoring for content changes

## 🔄 Content Update Workflow

1. **Plan**: Determine what content needs updating
2. **Draft**: Create or edit content in draft mode
3. **Review**: Use preview functionality to check formatting
4. **Approve**: Submit for editorial review if required
5. **Publish**: Make content live on the website
6. **Monitor**: Check that changes appear correctly on the site

## 📈 Performance Tips

- **Image Optimization**: Compress images before uploading
- **Content Length**: Keep excerpts concise and engaging
- **SEO**: Use relevant keywords naturally in content
- **Mobile**: Always preview content on mobile devices
- **Loading**: Avoid uploading very large files

---

**Last Updated**: December 2024
**Version**: 1.0
**Next Review**: March 2025
