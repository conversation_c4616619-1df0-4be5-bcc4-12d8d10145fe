[build]
  publish = "dist"
  command = "npm ci --legacy-peer-deps && npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_FLAGS = "--legacy-peer-deps"

# Redirect rules for SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Admin redirect
[[redirects]]
  from = "/admin"
  to = "/admin/"
  status = 301

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Cache images
[[headers]]
  for = "/uploads/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

# Admin security
[[headers]]
  for = "/admin/*"
  [headers.values]
    X-Robots-Tag = "noindex"
    Cache-Control = "no-cache"

# Enable Netlify Identity
[context.production]
  command = "npm ci --legacy-peer-deps && npm run build"

[context.deploy-preview]
  command = "npm ci --legacy-peer-deps && npm run build"

[context.branch-deploy]
  command = "npm ci --legacy-peer-deps && npm run build"
