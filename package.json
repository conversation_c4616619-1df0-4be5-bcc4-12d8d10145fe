{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "cms": "npx @decap/cli proxy-server", "cms:validate": "node scripts/validate-cms.js", "cms:dev": "concurrently \"npm run dev\" \"npm run cms\"", "test:cms": "npm run cms:validate && echo 'CMS validation passed!'"}, "dependencies": {"decap-cms": "^3.8.3", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.7.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/hast": "^3.0.4", "@types/mdast": "^4.0.4", "@types/node": "^24.2.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-redux": "^7.1.34", "@types/zen-observable": "^0.8.7", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.2.0", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "js-yaml": "^4.1.0", "netlify-cms-proxy-server": "^1.3.24", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}