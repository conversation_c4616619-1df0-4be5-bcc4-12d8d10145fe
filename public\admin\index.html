<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="robots" content="noindex" />
  <title>Farmland CMS - Content Manager</title>
  <link rel="icon" type="image/x-icon" href="/favicon.ico" />

  <!-- Netlify CMS Styles -->
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
    }

    /* Custom CMS styling */
    .nc-app-header {
      background: linear-gradient(135deg, #2A6F2D 0%, #3A8F3E 100%) !important;
    }

    .nc-app-header h1 {
      color: white !important;
    }

    /* Loading screen */
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #2A6F2D 0%, #3A8F3E 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      z-index: 9999;
    }

    .loading-spinner {
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top: 3px solid white;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin-right: 15px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div class="loading-screen" id="loading-screen">
    <div class="loading-spinner"></div>
    <div>Loading Farmland CMS...</div>
  </div>

  <!-- Decap CMS (Modern Netlify CMS) -->
  <script src="https://unpkg.com/decap-cms@^3.0.0/dist/decap-cms.js"></script>

  <!-- Custom CMS Configuration -->
  <script>
    // Hide loading screen when CMS loads
    window.addEventListener('load', function() {
      setTimeout(function() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          loadingScreen.style.display = 'none';
        }
      }, 1000);
    });

    // Custom preview styles
    if (window.CMS) {
      CMS.registerPreviewStyle('/admin/preview.css');
    }

    // Custom editor components can be added here
    // if (window.CMS) {
    //   CMS.registerEditorComponent({
    //   id: "youtube",
    //   label: "YouTube",
    //   fields: [{name: 'id', label: 'YouTube Video ID', widget: 'string'}],
    //   pattern: /^{{< youtube (\S+) >}}$/,
    //   fromBlock: function(match) {
    //     return {
    //       id: match[1]
    //     };
    //   },
    //   toBlock: function(obj) {
    //     return '{{< youtube ' + obj.id + ' >}}';
    //   },
    //   toPreview: function(obj) {
    //     return '<img src="http://img.youtube.com/vi/' + obj.id + '/maxresdefault.jpg" alt="Youtube Video"/>';
    //   }
    //   });
    // }
  </script>
</body>
</html>