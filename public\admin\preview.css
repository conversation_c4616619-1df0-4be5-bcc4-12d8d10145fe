/* Netlify CMS Preview Styles for Farmland Website */

/* Import Tailwind base styles for preview */
@import url('https://cdn.tailwindcss.com');

/* Custom CSS Variables matching the main site */
:root {
  /* Primary Green Color Palette */
  --primary-green: #2A6F2D;
  --primary-green-light: #3A8F3E;
  --primary-green-dark: #1F4F1F;
  --primary-green-lightest: #F0F9F0;
  
  /* Accent Orange Color Palette */
  --accent-orange: #F39C12;
  --accent-orange-light: #F5B041;
  --accent-orange-dark: #D68910;
  --accent-orange-lightest: #FEF9E7;
  
  /* Neutral Colors */
  --dark-text: #212529;
  --light-text: #555555;
  --section-bg: #F8F9FA;
  --white: #FFFFFF;
  --border-color: #DEE2E6;
  
  /* Earth Tones */
  --earth-brown: #8B6F47;
  --earth-light: #D4C5B9;
  --earth-dark: #5D4E37;
  
  /* Fonts */
  --font-heading: 'Montserrat', 'Inter', sans-serif;
  --font-body: 'Inter', system-ui, sans-serif;
}

/* Base preview styling */
.cms-preview-content {
  font-family: var(--font-body);
  line-height: 1.6;
  color: var(--dark-text);
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

/* Typography */
.cms-preview-content h1,
.cms-preview-content h2,
.cms-preview-content h3,
.cms-preview-content h4,
.cms-preview-content h5,
.cms-preview-content h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  color: var(--dark-text);
  margin-bottom: 1rem;
}

.cms-preview-content h1 {
  font-size: 2.5rem;
  color: var(--primary-green);
}

.cms-preview-content h2 {
  font-size: 2rem;
  color: var(--primary-green);
}

.cms-preview-content h3 {
  font-size: 1.5rem;
  color: var(--primary-green-dark);
}

.cms-preview-content p {
  margin-bottom: 1rem;
  color: var(--light-text);
}

/* Links */
.cms-preview-content a {
  color: var(--primary-green);
  text-decoration: underline;
}

.cms-preview-content a:hover {
  color: var(--primary-green-dark);
}

/* Lists */
.cms-preview-content ul,
.cms-preview-content ol {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

.cms-preview-content li {
  margin-bottom: 0.5rem;
  color: var(--light-text);
}

/* Images */
.cms-preview-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1rem 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Blockquotes */
.cms-preview-content blockquote {
  border-left: 4px solid var(--primary-green);
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: var(--light-text);
  background: var(--primary-green-lightest);
  padding: 1rem;
  border-radius: 4px;
}

/* Code blocks */
.cms-preview-content code {
  background: var(--section-bg);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
}

.cms-preview-content pre {
  background: var(--section-bg);
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1rem 0;
}

.cms-preview-content pre code {
  background: none;
  padding: 0;
}

/* Tables */
.cms-preview-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.cms-preview-content th,
.cms-preview-content td {
  border: 1px solid var(--border-color);
  padding: 0.75rem;
  text-align: left;
}

.cms-preview-content th {
  background: var(--primary-green);
  color: white;
  font-weight: 600;
}

.cms-preview-content tr:nth-child(even) {
  background: var(--section-bg);
}

/* Buttons (for preview) */
.cms-preview-content .btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: var(--primary-green);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  margin: 0.5rem 0;
  transition: background-color 0.3s ease;
}

.cms-preview-content .btn:hover {
  background: var(--primary-green-dark);
}

.cms-preview-content .btn-secondary {
  background: var(--accent-orange);
}

.cms-preview-content .btn-secondary:hover {
  background: var(--accent-orange-dark);
}

/* Cards (for preview) */
.cms-preview-content .card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1rem 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
}

/* Responsive design */
@media (max-width: 768px) {
  .cms-preview-content {
    padding: 15px;
  }
  
  .cms-preview-content h1 {
    font-size: 2rem;
  }
  
  .cms-preview-content h2 {
    font-size: 1.5rem;
  }
}

/* Special preview indicators */
.cms-preview-content::before {
  content: "🌱 Bharatvan Content Preview";
  display: block;
  background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-light) 100%);
  color: white;
  padding: 0.75rem 1rem;
  margin: -20px -20px 20px -20px;
  font-weight: 600;
  font-size: 0.9rem;
  border-radius: 8px 8px 0 0;
}

/* Custom callout boxes */
.callout {
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 8px;
  border-left: 4px solid;
}

.callout-info {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #0d47a1;
}

.callout-warning {
  background: #fff3e0;
  border-color: #ff9800;
  color: #e65100;
}

.callout-success {
  background: var(--primary-green-lightest);
  border-color: var(--primary-green);
  color: var(--primary-green-dark);
}

.callout-error {
  background: #ffebee;
  border-color: #f44336;
  color: #c62828;
}

.callout h4 {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.callout p {
  margin: 0;
}

/* YouTube embed preview */
.youtube-embed {
  background: #f5f5f5;
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  margin: 1rem 0;
}

.youtube-embed img {
  max-width: 100%;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

/* Farm card preview */
.farm-card-preview {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
}

.farm-card-preview h3 {
  color: var(--primary-green);
  margin-bottom: 0.5rem;
}

.farm-card-preview .price {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--accent-orange);
  margin: 0.5rem 0;
}

.farm-card-preview .status {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.farm-card-preview .status.ongoing {
  background: var(--primary-green-lightest);
  color: var(--primary-green);
}

.farm-card-preview .status.upcoming {
  background: var(--accent-orange-lightest);
  color: var(--accent-orange-dark);
}

.farm-card-preview .status.sold-out {
  background: #f5f5f5;
  color: #666;
}

/* Testimonial preview */
.testimonial-preview {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--primary-green);
}

.testimonial-preview .rating {
  color: #ffc107;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.testimonial-preview .customer-info {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
  font-weight: 600;
  color: var(--primary-green);
}

/* Form elements (for preview) */
.cms-preview-content input,
.cms-preview-content textarea,
.cms-preview-content select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-family: var(--font-body);
  margin: 0.5rem 0;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.cms-preview-content input:focus,
.cms-preview-content textarea:focus,
.cms-preview-content select:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(42, 111, 45, 0.1);
}

/* Badge styles */
.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background: var(--primary-green);
  color: white;
}

.badge-secondary {
  background: var(--accent-orange);
  color: white;
}

.badge-light {
  background: var(--section-bg);
  color: var(--dark-text);
}

/* Stats preview */
.stats-preview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-green);
  display: block;
}

.stat-label {
  color: var(--light-text);
  font-size: 0.9rem;
  margin-top: 0.25rem;
}
