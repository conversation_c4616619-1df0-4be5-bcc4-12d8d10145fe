@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Primary Green Color Palette */
    --primary-green: #2A6F2D;
    --primary-green-light: #3A8F3E;
    --primary-green-dark: #1F4F1F;
    --primary-green-lightest: #F0F9F0;
    
    /* Accent Orange Color Palette */
    --accent-orange: #F39C12;
    --accent-orange-light: #F5B041;
    --accent-orange-dark: #D68910;
    --accent-orange-lightest: #FEF9E7;
    
    /* Neutral Colors */
    --dark-text: #212529;
    --light-text: #555555;
    --section-bg: #F8F9FA;
    --white: #FFFFFF;
    --border-color: #DEE2E6;
    
    /* Earth Tones */
    --earth-brown: #8B6F47;
    --earth-light: #D4C5B9;
    --earth-dark: #5D4E37;
    
    /* Fonts */
    --font-heading: 'Montserrat', 'Inter', sans-serif;
    --font-body: 'Inter', system-ui, sans-serif;
    
    /* Shadows */
    --shadow-subtle: 0 4px 20px rgba(0,0,0,0.06);
    --shadow-lifted: 0 8px 30px rgba(0,0,0,0.12);
    --shadow-green: 0 4px 15px rgba(42, 111, 45, 0.15);
    --shadow-orange: 0 4px 15px rgba(243, 156, 18, 0.15);
  }

  body {
    font-family: var(--font-body);
    color: var(--dark-text);
    background-color: var(--white);
    line-height: 1.7;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 700;
  }

  .scrolled {
    background-color: var(--white);
    box-shadow: var(--shadow-subtle);
  }
}

@layer components {
  /* Button Styles */
  .btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 text-white px-6 py-3 sm:px-8 sm:py-4 rounded-2xl font-semibold transition-all duration-300 hover:from-primary-700 hover:to-primary-800 hover:shadow-xl hover:shadow-primary-600/25 transform hover:-translate-y-1 hover:scale-105 relative overflow-hidden;
  }
  
  .btn-primary::before {
    @apply content-[''] absolute top-0 left-0 w-full h-full bg-white opacity-0 transition-opacity duration-300;
  }
  
  .btn-primary:hover::before {
    @apply opacity-10;
  }
  
  .btn-secondary {
    @apply bg-white text-primary-600 border-2 border-primary-600 px-6 py-3 sm:px-8 sm:py-4 rounded-2xl font-semibold transition-all duration-300 hover:bg-primary-600 hover:text-white hover:shadow-xl hover:shadow-primary-600/25 transform hover:-translate-y-1 hover:scale-105 relative overflow-hidden;
  }

  .btn-accent {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600 text-white px-6 py-3 sm:px-8 sm:py-4 rounded-2xl font-semibold transition-all duration-300 hover:from-secondary-600 hover:to-secondary-700 hover:shadow-xl hover:shadow-secondary-500/25 transform hover:-translate-y-1 hover:scale-105 relative overflow-hidden;
  }
  
  .btn-accent::before {
    @apply content-[''] absolute top-0 left-0 w-full h-full bg-white opacity-0 transition-opacity duration-300;
  }
  
  .btn-accent:hover::before {
    @apply opacity-10;
  }
  
  /* Card Styles */
  .card {
    @apply bg-white rounded-3xl shadow-xl overflow-hidden transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 hover:scale-105 border border-gray-100;
  }
  
  .card-green {
    @apply bg-primary-green-lightest border border-primary-green-light/20;
  }
  
  .card-orange {
    @apply bg-accent-orange-lightest border border-accent-orange-light/20;
  }
  
  /* Text Styles */
  .text-gradient-green {
    @apply bg-gradient-to-r from-primary-green to-primary-green-light bg-clip-text text-transparent;
  }
  
  .text-gradient-orange {
    @apply bg-gradient-to-r from-accent-orange to-accent-orange-light bg-clip-text text-transparent;
  }
  
  /* Section Styles with Rounded Corners */
  .section-rounded {
    @apply rounded-2xl overflow-hidden;
  }
  
  .section-rounded-lg {
    @apply rounded-3xl overflow-hidden;
  }
  
  .section-rounded-xl {
    @apply rounded-[2rem] overflow-hidden;
  }
  
  /* Card with Decorative Elements */
  .card-decorated {
    @apply relative rounded-xl overflow-hidden;
  }
  
  .card-decorated::before {
    @apply content-[''] absolute w-16 h-16 -top-8 -right-8 bg-primary-100 rounded-full opacity-50;
  }
  
  .card-decorated::after {
    @apply content-[''] absolute w-12 h-12 -bottom-6 -left-6 bg-secondary-100 rounded-full opacity-50;
  }
  
  /* Nav Tab Styles */
  .nav-tab {
    @apply relative px-4 py-2 font-medium transition-all duration-300 rounded-lg;
  }
  
  .nav-tab-active {
    @apply bg-primary-50 text-primary-600;
  }
  
  .nav-tab::after {
    @apply content-[''] absolute bottom-0 left-1/2 w-0 h-0.5 bg-primary-500 transition-all duration-300 -translate-x-1/2;
  }
  
  .nav-tab:hover::after {
    @apply w-1/2;
  }
  
  .nav-tab-active::after {
    @apply w-2/3;
  }
  
  /* Responsive Layout Utilities */
  .container-mobile {
    @apply px-4 w-full mx-auto;
  }
  
  .container-responsive {
    @apply px-4 sm:px-6 lg:px-8 w-full mx-auto max-w-7xl;
  }
  
  .flex-responsive {
    @apply flex flex-col sm:flex-row;
  }
  
  .grid-responsive-1 {
    @apply grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6;
  }
  
  .grid-responsive-2 {
    @apply grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6;
  }
  
  .grid-responsive-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;
  }
  
  /* Responsive Typography System */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-xs sm:text-sm lg:text-base;
  }

  .text-responsive-base {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .text-responsive-lg {
    @apply text-base sm:text-lg lg:text-xl;
  }

  .text-responsive-xl {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  .text-responsive-2xl {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  .text-responsive-3xl {
    @apply text-xl sm:text-2xl lg:text-3xl xl:text-4xl;
  }

  .text-responsive-4xl {
    @apply text-2xl sm:text-3xl lg:text-4xl xl:text-5xl;
  }

  .text-responsive-5xl {
    @apply text-2xl sm:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl;
  }

  .text-responsive-6xl {
    @apply text-3xl sm:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl;
  }

  /* Mobile-first heading styles */
  .heading-mobile-sm {
    @apply text-lg sm:text-xl lg:text-2xl font-bold;
  }

  .heading-mobile-md {
    @apply text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold;
  }

  .heading-mobile-lg {
    @apply text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold;
  }

  .heading-mobile-xl {
    @apply text-2xl sm:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl font-bold;
  }

  /* Compact spacing for mobile */
  .spacing-mobile-tight {
    @apply space-y-2 sm:space-y-3 lg:space-y-4;
  }

  .spacing-mobile-normal {
    @apply space-y-3 sm:space-y-4 lg:space-y-6;
  }

  .spacing-mobile-loose {
    @apply space-y-4 sm:space-y-6 lg:space-y-8;
  }

  /* Mobile-optimized padding */
  .padding-mobile-sm {
    @apply p-3 sm:p-4 lg:p-6;
  }

  .padding-mobile-md {
    @apply p-4 sm:p-6 lg:p-8;
  }

  .padding-mobile-lg {
    @apply p-6 sm:p-8 lg:p-12;
  }

  /* Mobile-optimized margins */
  .margin-mobile-sm {
    @apply m-2 sm:m-3 lg:m-4;
  }

  .margin-mobile-md {
    @apply m-3 sm:m-4 lg:m-6;
  }

  .margin-mobile-lg {
    @apply m-4 sm:m-6 lg:m-8;
  }

  /* Mobile section padding */
  .section-mobile-sm {
    @apply py-6 sm:py-8 lg:py-12;
  }

  .section-mobile-md {
    @apply py-8 sm:py-12 lg:py-16;
  }

  .section-mobile-lg {
    @apply py-12 sm:py-16 lg:py-20;
  }

  /* Mobile container padding */
  .container-mobile-padding {
    @apply px-3 sm:px-4 lg:px-8;
  }

  /* Mobile-optimized grid layouts */
  .grid-mobile-1 {
    @apply grid grid-cols-1 gap-3 sm:gap-4 lg:gap-6;
  }

  .grid-mobile-2 {
    @apply grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 lg:gap-6;
  }

  .grid-mobile-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6;
  }

  .grid-mobile-4 {
    @apply grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6;
  }

  /* Mobile-optimized flex layouts */
  .flex-mobile-col {
    @apply flex flex-col sm:flex-row gap-3 sm:gap-4 lg:gap-6;
  }

  /* Mobile line clamping */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Animation Classes */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown 0.5s ease-out;
  }
  
  .animate-zoom-in {
    animation: zoomIn 0.5s ease-out;
  }
  
  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from { 
    opacity: 0;
    transform: translateY(-30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  from { 
    opacity: 0;
    transform: translateX(30px);
  }
  to { 
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from { 
    opacity: 0;
    transform: translateX(-30px);
  }
  to { 
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes zoomIn {
  from { 
    opacity: 0;
    transform: scale(0.9);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
